'use client';

import React, { useState, useEffect, useCallback } from 'react';
import { useSession } from 'next-auth/react';
import { useRouter } from 'next/navigation';
import { useF<PERSON>, Controller, SubmitHandler, FieldError, Path } from 'react-hook-form';

import QuestionWrapper from '@/components/assessment/QuestionWrapper';
import MultipleChoiceQuestion from '@/components/assessment/MultipleChoiceQuestion';
import ScaleQuestion from '@/components/assessment/ScaleQuestion';
import TextQuestion from '@/components/assessment/TextQuestion';
import { assessmentDefinition, Question as AssessmentQuestionConfig, MCQuestion, ScQuestion, TextQuestion as TextQuestionConfig } from '@/lib/assessmentDefinition';
import AssessmentResults from '@/components/assessment/AssessmentResults';
import PageLayout from '@/components/layout/PageLayout';
import { log } from '@/lib/logger';
import { useCSRFToken } from '@/hooks/useCSRFToken';
import { FeedbackButton } from '@/components/ui/feedback-button';
import { getUserFriendlyError } from '@/lib/user-friendly-errors';
import { ErrorBoundary } from '@/components/unified-error-boundary';
import { LoadingState } from '@/components/ui/error-feedback';
import { injectStructuredData } from '@/lib/seo/seo-utils';

// Define types for questions and steps
// MOVED to @/lib/assessmentDefinition.ts
/*
interface Option {
  value: string;
  label: string;
}

interface BaseQuestion {
  key: string;
  text: string;
  description?: string;
}

interface MCQuestion extends BaseQuestion {
  type: 'multipleChoice';
  options: Option[];
  allowMultiple?: boolean;
}

interface ScQuestion extends BaseQuestion {
  type: 'scale';
  minLabel: string;
  maxLabel: string;
  numberOfSteps: number;
}

type Question = MCQuestion | ScQuestion;

interface AssessmentStep {
  step: number;
  title: string;
  questions: Question[];
}
*/
// --- End Sample Assessment Definition ---

interface AssessmentFormData {
  [questionKey: string]: string | string[] | number | null;
}

// Type for the data structure returned by our GET /api/assessment endpoint
interface FetchedAssessmentProgress {
  currentStep: number;
  formData: AssessmentFormData;
  status: string; // e.g., IN_PROGRESS, COMPLETED
  updatedAt: string;
  id: string; // Assessment ID
}

// Type for the data structure returned by POST /api/assessment
interface SavedAssessmentResponse {
  message: string;
  assessmentId: string;
  status: string;
}

interface AssessmentSavePayload {
  currentStep: number;
  formData: AssessmentFormData;
  status?: 'IN_PROGRESS' | 'COMPLETED';
}

// API interaction functions (implementing placeholders from previous step)
const fetchSavedProgressAPI = async (): Promise<FetchedAssessmentProgress | null> => {
  try {
    const response = await fetch('/api/assessment', {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
      credentials: 'include', // Include cookies for session
    });

    if (response.status === 404) {
      return null; // No active assessment found
    }

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({
        error: { message: `Failed to fetch progress: ${response.status} ${response.statusText}` }
      }));
      // Handle both old and new error response formats
      const errorMessage = errorData.error?.message || errorData.error || `Failed to fetch progress: ${response.statusText}`;
      throw new Error(errorMessage);
    }

    const responseData = await response.json();
    // Handle structured API response format { success: true, data: { ... } }
    if (responseData.success && responseData.data) {
      return responseData.data;
    }
    // Fallback for direct response format
    return responseData;
  } catch (error) {
    if (error instanceof TypeError && error instanceof Error ? error.message : String(error).includes('fetch')) {
      throw new Error('Network error: Unable to connect to the server. Please check your internet connection.');
    }
    throw error;
  }
};

const saveProgressAPIClient = async (currentStep: number, formData: AssessmentFormData, status?: 'IN_PROGRESS' | 'COMPLETED', csrfFetch?: any): Promise<SavedAssessmentResponse> => {
  const payload: AssessmentSavePayload = {
    currentStep,
    formData,
  };
  if (status) {
    payload.status = status;
  }

  try {
    const fetchFunction = csrfFetch || fetch;
    const response = await fetchFunction('/api/assessment', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      credentials: 'include', // Include cookies for session
      body: JSON.stringify(payload),
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({
        error: { message: `Failed to save progress: ${response.status} ${response.statusText}` }
      }));
      // Handle both old and new error response formats
      const errorMessage = errorData.error?.message || errorData.error || `Failed to save progress: ${response.statusText}`;
      throw new Error(errorMessage);
    }

    const responseData = await response.json();
    // Handle structured API response format { success: true, data: { ... } }
    if (responseData.success && responseData.data) {
      return responseData.data;
    }
    // Fallback for direct response format
    return responseData;
  } catch (error) {
    if (error instanceof TypeError && error instanceof Error ? error.message : String(error).includes('fetch')) {
      throw new Error('Network error: Unable to save progress. Please check your internet connection.');
    }
    throw error;
  }
};

const submitAssessmentAPI = async (assessmentId: string, formData: AssessmentFormData, csrfFetch?: any): Promise<{ message: string; assessmentId: string; status: string; }> => {
  const fetchFunction = csrfFetch || fetch;
  const response = await fetchFunction('/api/assessment', { // Backend handles PUT on the same route
    method: 'PUT',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({ assessmentId, formData }),
  });
  if (!response.ok) {
    const errorData = await response.json().catch(() => ({ error: 'Failed to submit assessment, and error response was not valid JSON.' }));
    // Handle both old and new error response formats
    const errorMessage = errorData.error?.message || errorData.error || `Failed to submit assessment: ${response.statusText}`;
    throw new Error(errorMessage);
  }

  const responseData = await response.json();
  // Handle structured API response format { success: true, data: { ... } }
  if (responseData.success && responseData.data) {
    return responseData.data;
  }
  // Fallback for direct response format
  return responseData;
};

function AssessmentPageContent() {
  const { data: session, status: sessionStatus } = useSession();
  const router = useRouter();
  const { csrfFetch, isLoading: csrfLoading } = useCSRFToken();
  const [currentStep, setCurrentStep] = useState(1);
  const [isLoading, setIsLoading] = useState(true);

  // Inject SEO structured data
  useEffect(() => {
    const structuredData = {
      '@context': 'https://schema.org',
      '@type': 'WebApplication',
      name: 'FAAFO Career Assessment',
      description: 'Comprehensive career assessment tool for discovering strengths and career matches.',
      url: `${process.env.NEXT_PUBLIC_BASE_URL || 'https://faafo-career.com'}/assessment`,
      applicationCategory: 'EducationalApplication',
      operatingSystem: 'Web Browser',
      offers: {
        '@type': 'Offer',
        price: '0',
        priceCurrency: 'USD',
      },
      provider: {
        '@type': 'Organization',
        name: 'FAAFO Career Platform',
        url: process.env.NEXT_PUBLIC_BASE_URL || 'https://faafo-career.com',
      },
    };

    injectStructuredData(structuredData);
  }, []);
  const [error, setError] = useState<string | null>(null);
  const [isSaving, setIsSaving] = useState(false);
  const [assessmentId, setAssessmentId] = useState<string | null>(null); // Store assessment ID
  const [isAssessmentCompleted, setIsAssessmentCompleted] = useState(false); // Added state
  const [debugInfo, setDebugInfo] = useState<string>(''); // Debug information

  const {
    control,
    handleSubmit,
    watch,
    formState: { errors, isDirty, isValid },
    reset,
    trigger,
  } = useForm<AssessmentFormData>({
    defaultValues: {},
    mode: 'onChange',
  });

  const allFormValues = watch();

  useEffect(() => {
    setDebugInfo(`Session: ${sessionStatus}, User: ${session?.user?.id ? 'Yes' : 'No'}, Completed: ${isAssessmentCompleted}`);

    // Handle different session states
    if (sessionStatus === 'loading') {
      // Still loading session, keep loading state
      return;
    }

    if (sessionStatus === 'unauthenticated') {
      // Not authenticated, redirect to login
      router.push('/login');
      return;
    }

    // Only fetch/load progress if authenticated and assessment isn't completed
    if (sessionStatus === 'authenticated' && session?.user?.id && !isAssessmentCompleted) {
      setIsLoading(true);
      setError(null);

      // Add timeout to prevent infinite loading
      const timeoutId = setTimeout(() => {
        console.log('Assessment loading timeout reached');
        setError('Loading timeout. Please refresh the page.');
        setIsLoading(false);
      }, 10000); // 10 second timeout

      fetchSavedProgressAPI()
        .then(savedProgress => {
          clearTimeout(timeoutId);
          if (savedProgress) {
            if (savedProgress.status === 'COMPLETED') {
              setAssessmentId(savedProgress.id);
              setIsAssessmentCompleted(true);
              log.info('Assessment already completed, showing suggestions', {
                component: 'assessment_page',
                action: 'load_completed_assessment',
                metadata: { assessmentId: savedProgress.id }
              });
            } else {
              // Validate currentStep before setting it
              const validCurrentStep = savedProgress.currentStep &&
                                     typeof savedProgress.currentStep === 'number' &&
                                     savedProgress.currentStep >= 1 &&
                                     savedProgress.currentStep <= assessmentDefinition.length
                                     ? savedProgress.currentStep
                                     : 1;

              setCurrentStep(validCurrentStep);
              reset(savedProgress.formData);
              setAssessmentId(savedProgress.id);
              log.info('Assessment progress loaded', {
                component: 'assessment_page',
                action: 'load_progress',
                metadata: {
                  assessmentId: savedProgress.id,
                  currentStep: validCurrentStep,
                  originalCurrentStep: savedProgress.currentStep
                }
              });
            }
          } else {
            reset({});
            setCurrentStep(1);
            setAssessmentId(null);
            log.info('No active assessment found, starting fresh', {
              component: 'assessment_page',
              action: 'start_new_assessment'
            });
          }
        })
        .catch(err => {
          clearTimeout(timeoutId);
          console.error('Error fetching saved progress:', err);
          // Don't show error for 404 (no assessment found), just start fresh
          if (err.message?.includes('404') || err.message?.includes('not found') || err.message?.includes('No active assessment found')) {
            reset({});
            setCurrentStep(1);
            setAssessmentId(null);
            log.info('No existing assessment found, starting fresh', {
              component: 'assessment_page',
              action: 'start_new_assessment_after_404'
            });
          } else {
            setError(err.message || 'Could not load your saved progress. Starting fresh.');
            reset({});
            setCurrentStep(1);
            setAssessmentId(null);
          }
        })
        .finally(() => {
          setIsLoading(false);
        });
    }
  }, [sessionStatus, session, router, reset, isAssessmentCompleted]);

  const totalSteps = assessmentDefinition.length;
  const currentStepData = assessmentDefinition.find(s => s.step === currentStep);

  // Debug logging for step configuration issues
  if (!currentStepData) {
    console.error('Assessment step configuration error:', {
      currentStep,
      currentStepType: typeof currentStep,
      totalSteps,
      availableSteps: assessmentDefinition.map(s => s.step),
      assessmentDefinitionLength: assessmentDefinition.length
    });
  }

  const handleNext = async () => {
    const currentQuestionKeys = currentStepData?.questions.map(q => q.key as Path<AssessmentFormData>) || [];
    const validationResult = await trigger(currentQuestionKeys);

    if (validationResult && currentStep < totalSteps) {
      setCurrentStep(prev => prev + 1);
    }
  };

  const handlePrevious = () => {
    if (currentStep > 1) {
      setCurrentStep(prev => prev - 1);
    }
  };

  const processSave = useCallback(async (data: AssessmentFormData, isAutoSave = false) => {
    if (!session?.user?.id) {
      if (!isAutoSave) setError('Cannot save progress: User not authenticated.');
      return;
    }
    setIsSaving(true);
    if (!isAutoSave) setError(null);
    log.debug('Saving assessment progress', {
      component: 'assessment_page',
      action: 'save_progress',
      metadata: { currentStep, isAutoSave }
    });
    try {
      const result = await saveProgressAPIClient(currentStep, data, undefined, csrfFetch);
      setAssessmentId(result.assessmentId);
      if (!isAutoSave) alert('Progress saved successfully!');
      log.info('Assessment progress saved successfully', {
        component: 'assessment_page',
        action: 'save_progress_success',
        metadata: {
          assessmentId: result.assessmentId,
          currentStep,
          isAutoSave
        }
      });
      reset(data, { keepValues: true, keepDirty: false, keepDefaultValues: false });
    } catch (apiError) {
      log.error('Error saving assessment progress', apiError as Error, {
        component: 'assessment_page',
        action: 'save_progress_error',
        metadata: { currentStep, isAutoSave }
      });
      const message = apiError instanceof Error ? apiError.message : String(apiError);
      if (!isAutoSave) {
        setError(message);
        alert(`Error saving: ${message}`);
      }
    }
    setIsSaving(false);
  }, [session, currentStep, reset, csrfFetch]);

  useEffect(() => {
    const timer = setTimeout(() => {
      if (isDirty && session?.user?.id) {
        log.debug('Auto-saving dirty form data', {
          component: 'assessment_page',
          action: 'auto_save_trigger'
        });
        processSave(allFormValues, true);
      }
    }, 5000);

    return () => clearTimeout(timer);
  }, [allFormValues, isDirty, processSave, session]);

  // Wrapper for SubmitButton
  const handleSubmitWrapper = async () => {
    const data = allFormValues;
    await processSubmit(data);
  };

  const processSubmit: SubmitHandler<AssessmentFormData> = async (data) => {
    if (currentStep !== totalSteps) {
      const errorMessage = 'Please complete all steps before submitting.';
      setError(errorMessage);
      throw new Error(errorMessage);
    }
    const allQuestionKeys = assessmentDefinition.flatMap(step => step.questions.map(q => q.key as Path<AssessmentFormData>));
    const validationResult = await trigger(allQuestionKeys);

    if (!validationResult) {
      const errorMessage = 'Please ensure all questions are answered correctly.';
      setError(errorMessage);
      throw new Error(errorMessage);
    }

    if (!session?.user?.id) {
      const errorMessage = 'Cannot submit assessment: User not authenticated.';
      setError(errorMessage);
      throw new Error(errorMessage);
    }
    setError(null);
    log.info('Submitting assessment', {
      component: 'assessment_page',
      action: 'submit_assessment',
      metadata: { currentStep, totalSteps }
    });

    let currentAssessmentId = assessmentId; // Use state first

    try {

      // If no assessmentId exists yet (e.g., first time, no auto-save triggered or completed)
      // we must save to create the assessment record and get an ID.
      if (!currentAssessmentId) {
        log.info('No existing assessmentId found, saving progress to create one before final submission', {
          component: 'assessment_page',
          action: 'pre_submission_save'
        });
        try {
          const saveResult = await saveProgressAPIClient(currentStep, data, 'IN_PROGRESS', csrfFetch); // Save as IN_PROGRESS first
          currentAssessmentId = saveResult.assessmentId;
          setAssessmentId(currentAssessmentId); // Update state for future use
          log.info('Pre-submission save successful', {
            component: 'assessment_page',
            action: 'pre_submission_save_success',
            metadata: { assessmentId: currentAssessmentId }
          });
        } catch (saveError) {
          log.error('Error during pre-submission save', saveError as Error, {
            component: 'assessment_page',
            action: 'pre_submission_save_error'
          });
          const friendlyError = getUserFriendlyError(saveError, 'assessment');
          setError(friendlyError.message);
          throw new Error(friendlyError.message);
        }
      }

      // Now, currentAssessmentId should be valid.
      log.info('Proceeding with final submission', {
        component: 'assessment_page',
        action: 'final_submission',
        metadata: { assessmentId: currentAssessmentId }
      });
      const result = await submitAssessmentAPI(currentAssessmentId!, data, csrfFetch); // Pass the obtained/existing assessmentId
      
      // Update state with the ID from the final submission result as well, though it should match
      setAssessmentId(result.assessmentId);
      reset(data, { keepValues: true, keepDirty: false, keepDefaultValues: false });

      if (result.status === 'COMPLETED') {
        setIsAssessmentCompleted(true);
        alert('Assessment submitted successfully!');
        log.info('Assessment submission successful with COMPLETED status', {
          component: 'assessment_page',
          action: 'submit_completed',
          metadata: {
            assessmentId: result.assessmentId,
            status: result.status
          }
        });
        return;
      }

      alert('Assessment data saved successfully! (Status not completed)');
      log.warn('Assessment submission successful but status not COMPLETED', {
        component: 'assessment_page',
        action: 'submit_incomplete',
        metadata: {
          assessmentId: result.assessmentId,
          status: result.status
        }
      });

    } catch (apiError) {
      log.error('Error during assessment submission', apiError as Error, {
        component: 'assessment_page',
        action: 'submit_error',
        metadata: { assessmentId: currentAssessmentId }
      });
      const friendlyError = getUserFriendlyError(apiError, 'assessment');
      setError(friendlyError.message);
      throw new Error(friendlyError.message);
    }
  };

  if (sessionStatus === 'loading' || isLoading) {
    return (
      <PageLayout maxWidth="2xl" padding="lg">
        <div className="flex flex-col justify-center items-center min-h-[400px] space-y-4">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
          <p className="text-lg text-gray-600 dark:text-gray-400">Loading assessment...</p>
          <p className="text-sm text-gray-500 dark:text-gray-500">
            {sessionStatus === 'loading' ? 'Checking authentication...' : 'Loading your progress...'}
          </p>
          {process.env.NODE_ENV === 'development' && (
            <div className="mt-4 p-3 bg-gray-100 dark:bg-gray-800 rounded text-xs">
              <p>Debug: {debugInfo}</p>
            </div>
          )}
        </div>
      </PageLayout>
    );
  }

  if (sessionStatus === 'unauthenticated') {
    return (
      <PageLayout maxWidth="2xl" padding="lg">
        <div className="flex flex-col justify-center items-center min-h-[400px] space-y-4">
          <p className="text-lg text-gray-600 dark:text-gray-400">Redirecting to login...</p>
        </div>
      </PageLayout>
    );
  }

  if (error) {
    return (
      <PageLayout maxWidth="2xl" padding="lg">
        <div className="text-center py-8">
          <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-6">
            <h2 className="text-lg font-semibold text-red-800 dark:text-red-200 mb-2">
              Assessment Loading Error
            </h2>
            <p className="text-red-600 dark:text-red-300 mb-4">{error}</p>
            <div className="space-x-4">
              <button
                onClick={() => {
                  setError(null);
                  setIsLoading(true);
                  window.location.reload();
                }}
                className="px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 transition-colors"
              >
                Retry
              </button>
              <button
                onClick={() => {
                  setError(null);
                  setCurrentStep(1);
                  reset({});
                  setAssessmentId(null);
                }}
                className="px-4 py-2 bg-gray-600 text-white rounded-md hover:bg-gray-700 transition-colors"
              >
                Start Fresh
              </button>
            </div>
          </div>
        </div>
      </PageLayout>
    );
  }

  if (isAssessmentCompleted && assessmentId) {
    return <AssessmentResults assessmentId={assessmentId} />;
  }

  if (!currentStepData) {
    return (
      <PageLayout maxWidth="2xl" padding="lg">
        <div className="text-center py-10">
          <p className="text-red-500 mb-4">
            Error: Assessment step configuration not found for step "{currentStep}" (type: {typeof currentStep}).
          </p>
          <p className="text-gray-600 dark:text-gray-400 mb-4">
            Available steps: {assessmentDefinition.map(s => s.step).join(', ')} (Total: {totalSteps})
          </p>
          <button
            onClick={() => {
              setCurrentStep(1);
              setError(null);
            }}
            className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
          >
            Reset to Step 1
          </button>
        </div>
      </PageLayout>
    );
  }
  
  const currentQuestionKeys = currentStepData.questions.map(q => q.key);
  // eslint-disable-next-line security/detect-object-injection
  const isCurrentStepValid = currentQuestionKeys.every(key => !errors[key]);

  return (
    <PageLayout maxWidth="2xl" padding="lg">
      <h1 className="text-3xl font-bold mb-2">Self-Assessment Questionnaire</h1>
      <p className="text-lg text-gray-600 dark:text-gray-400 mb-6">Step {currentStep} of {totalSteps}: {currentStepData.title}</p>

      <div className="bg-gray-200 dark:bg-gray-700 rounded-full h-2.5 mb-8">
        <div
          className="bg-blue-600 h-2.5 rounded-full transition-all duration-500 ease-out"
          style={{ width: `${(currentStep / totalSteps) * 100}%` }}
        ></div>
      </div>

      {error && <p className="my-4 text-center text-red-500 bg-red-100 dark:bg-red-900 dark:text-red-200 p-3 rounded-md" role="alert">Error: {error}</p>}

      <form onSubmit={handleSubmit(processSubmit)} className="space-y-8">
        {currentStepData.questions.map(q => {
          const questionConfig = q as AssessmentQuestionConfig;
          const fieldError = errors[questionConfig.key] as FieldError | undefined;
          return (
            <QuestionWrapper
              key={questionConfig.key}
              questionKey={questionConfig.key}
              questionText={questionConfig.text}
              description={questionConfig.description}
              error={fieldError?.message}
            >
              {questionConfig.type === 'multipleChoice' && (
                <Controller
                  name={questionConfig.key}
                  control={control}
                  rules={{ 
                    required: questionConfig.required ? 'This field is required.' : false,
                    validate: questionConfig.allowMultiple && questionConfig.required ? (value) => Array.isArray(value) && value.length > 0 || 'Please select at least one option.' : undefined
                  }}
                  render={({ field }) => {
                    let selectionValue: string | string[] = [];
                    if (typeof field.value === 'string') {
                      selectionValue = questionConfig.allowMultiple ? [field.value] : field.value;
                    } else if (Array.isArray(field.value)) {
                      selectionValue = field.value.filter(item => typeof item === 'string') as string[];
                    } else if (field.value === null || field.value === undefined) {
                        selectionValue = questionConfig.allowMultiple ? [] : '';
                    }
                    if (!questionConfig.allowMultiple && Array.isArray(selectionValue)) {
                        selectionValue = selectionValue[0] || '';
                    }
                    return (
                      <MultipleChoiceQuestion
                        questionKey={questionConfig.key}
                        options={(questionConfig as MCQuestion).options}
                        currentValue={selectionValue}
                        onChange={(_questionKey, selection) => field.onChange(selection)}
                        allowMultiple={questionConfig.allowMultiple}
                      />
                    );
                  }}
                />
              )}
              {questionConfig.type === 'scale' && (
                <Controller
                  name={questionConfig.key}
                  control={control}
                  rules={{ required: questionConfig.required ? 'This field is required.' : false }}
                  render={({ field }) => {
                    let scaleValue: number | null = null;
                    if (typeof field.value === 'number') {
                      scaleValue = field.value;
                    } else if (typeof field.value === 'string') {
                      const parsed = parseInt(field.value, 10);
                      if (!isNaN(parsed)) {
                        scaleValue = parsed;
                      }
                    }
                    if (typeof scaleValue !== 'number') scaleValue = null;
                    return (
                      <ScaleQuestion
                        questionKey={questionConfig.key}
                        minLabel={(questionConfig as ScQuestion).minLabel}
                        maxLabel={(questionConfig as ScQuestion).maxLabel}
                        numberOfSteps={(questionConfig as ScQuestion).numberOfSteps}
                        currentValue={scaleValue}
                        onChange={(_questionKey, value) => field.onChange(value)}
                      />
                    );
                  }}
                />
              )}
              {questionConfig.type === 'text' && (
                <Controller
                  name={questionConfig.key}
                  control={control}
                  rules={{
                    required: questionConfig.required ? 'This field is required.' : false,
                    minLength: (questionConfig as TextQuestionConfig).minLength ? {
                      value: (questionConfig as TextQuestionConfig).minLength!,
                      message: `Minimum ${(questionConfig as TextQuestionConfig).minLength} characters required.`
                    } : undefined,
                    maxLength: (questionConfig as TextQuestionConfig).maxLength ? {
                      value: (questionConfig as TextQuestionConfig).maxLength!,
                      message: `Maximum ${(questionConfig as TextQuestionConfig).maxLength} characters allowed.`
                    } : undefined
                  }}
                  render={({ field }) => {
                    const textValue = typeof field.value === 'string' ? field.value : '';
                    return (
                      <TextQuestion
                        questionKey={questionConfig.key}
                        placeholder={(questionConfig as TextQuestionConfig).placeholder}
                        maxLength={(questionConfig as TextQuestionConfig).maxLength}
                        minLength={(questionConfig as TextQuestionConfig).minLength}
                        currentValue={textValue}
                        onChange={(_questionKey, value) => field.onChange(value)}
                      />
                    );
                  }}
                />
              )}
            </QuestionWrapper>
          );
        })}

        <div className="mt-10 flex justify-between items-center">
          <button
            type="button"
            onClick={handlePrevious}
            disabled={currentStep === 1 || isSaving || isLoading}
            className="px-6 py-2 border border-gray-300 dark:border-gray-600 rounded-md text-sm font-medium text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 disabled:opacity-50 transition-colors"
          >
            Previous
          </button>

          {currentStep < totalSteps && (
            <button
              type="button"
              onClick={handleNext}
              disabled={isSaving || isLoading || !isCurrentStepValid}
              className="px-6 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 disabled:opacity-50 disabled:bg-gray-400 transition-colors"
            >
              Next
            </button>
          )}

          {currentStep === totalSteps && (
            <FeedbackButton
              onClick={handleSubmitWrapper}
              disabled={isLoading || !isValid}
              context="assessment"
              loadingText="Submitting Assessment..."
              successText="Assessment Submitted!"
              retryable={true}
              className="px-6 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-green-600 hover:bg-green-700 disabled:opacity-50 disabled:bg-gray-400 transition-colors"
            >
              Submit Assessment
            </FeedbackButton>
          )}
        </div>
        <div className="mt-6 text-center">
          <button
            type="button"
            onClick={() => processSave(allFormValues, false)}
            disabled={isSaving || isLoading || !isDirty }
            className="px-4 py-2 text-sm font-medium text-blue-600 dark:text-blue-400 hover:underline disabled:opacity-50 disabled:text-gray-400 dark:disabled:text-gray-500 transition-colors"
          >
            {isSaving ? 'Saving...' : 'Save Progress'}
          </button>
        </div>
      </form>
    </PageLayout>
  );
}

// Wrap the assessment page with error boundary and loading states
export default function AssessmentPage() {
  return (
    <ErrorBoundary
      fallback={
        <PageLayout>
          <div className="max-w-4xl mx-auto p-6 text-center">
            <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-6">
              <h2 className="text-lg font-semibold text-red-800 dark:text-red-200 mb-2">
                Assessment Error
              </h2>
              <p className="text-red-600 dark:text-red-300 mb-4">
                There was an issue loading the skills assessment. Please try refreshing the page.
              </p>
              <button
                onClick={() => window.location.reload()}
                className="px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 transition-colors"
              >
                Refresh Page
              </button>
            </div>
          </div>
        </PageLayout>
      }
      onError={(error, errorInfo) => {
        console.error('Assessment Page Error:', { error, errorInfo });
      }}
    >
      <AssessmentPageContent />
    </ErrorBoundary>
  );
}